from multiprocessing import Process, Pipe


class Manager:
    def __init__(self, target, argsQueue, numWorkers, result_callback=None):
        self.numWorkers = numWorkers
        self.target = target
        self.argsQ = argsQueue
        self.tracker = 0
        self.result_callback = result_callback  # Callback function to call when each result is ready
    
    def processLoop(self):
        self.processes = {}
        self.pipes = {}
        results = []
        minLength = min(self.numWorkers, len(self.argsQ))
        # create an initial number of processes
        for i in range(minLength):
            args = list(self.argsQ[i]) # original like [(answerSheetsbytes, (rubricfilename, subject)) ...]
            parent_conn, child_conn = Pipe()
            args.append(child_conn)
            process = Process(target=self.target, args=tuple(args))
            self.processes[i] = process
            self.pipes[i] = (parent_conn, child_conn)
            self.tracker += 1
            process.start()
            self.pipes[i][1].close()

        while(len(results) < len(self.argsQ)):
            for key in self.pipes.keys(): # it will run num worker times
                if (self.pipes[key] and self.pipes[key][0].poll()):
                    result = self.pipes[key][0].recv()
                    results.append(result)

                    # Call the callback function immediately if provided
                    if self.result_callback:
                        try:
                            self.result_callback(result)
                        except Exception as e:
                            print(f"Error in result callback: {e}")

                    self.pipes[key][0].close() # close the parent end
                    self.pipes[key] = None # mark it as closed
                    # process finished here
                    self.processes[key].join() # free up the resources
                    # create a new process with a new target
                    if (self.tracker < len(self.argsQ)):
                        args = list(self.argsQ[self.tracker])
                        parent_conn, child_conn = Pipe()
                        args.append(child_conn)
                        process = Process(target=self.target, args=tuple(args))
                        self.processes[key] = process
                        self.pipes[key] = (parent_conn, child_conn)
                        self.tracker += 1
                        process.start()
                        self.pipes[key][1].close() # close the child end in the parent process
        return results

